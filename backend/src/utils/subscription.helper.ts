import {
  QIBillingInterval,
  QISubscription,
  QISubscriptionItem,
} from 'src/modules/subscription/types/qiplus.types';

import { QISubscriptionStatus } from 'src/modules/subscription/types/qiplus.types';

import { Account } from 'src/modules/account/model/account.model';
import { CreatePaymentDto } from 'src/modules/payment/dto/create-payment.dto';
import { PaymentGateway } from 'src/modules/payment/types/gateway.types';
import { Plan } from 'src/modules/plan/model/plan.model';
import { QIPaymentMethod } from 'src/modules/subscription/types/qiplus.types';
import {
  PlanPreviewDto,
  UpgradePreviewDto,
} from 'src/modules/upgrade/dto/upgrade-preview.dto';
import { calculateInstallmentDueDate } from 'src/modules/payment/utils/payment.utils';
import { PaymentMethod } from 'src/modules/payment/enum/paymentMethod.enum';
import { addDays, addYears, currentTimestamp, formatForLog, getDate, toFirebaseTimestamp } from './date.utils';

/**
 * Calculate the next billing date for yearly subscriptions based on the billing day
 * @param billingDay The day of the month for billing
 * @param currentDateOverride Optional date to use instead of the current date (useful for testing)
 * @returns The next billing date
 */
export function calculateYearlyBillingDate(billingDay: number, currentDateOverride?: number): number {
  // Use provided date or current date
  const currentDate = toFirebaseTimestamp(currentDateOverride || currentTimestamp()).toDate();

  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const currentDay = currentDate.getDate();

  // Create a date for the billing day in the current month
  const nextBillingDate = new Date(year, month, billingDay);

  // If the billing day is in the past (less than current day), move to next month
  if (billingDay < currentDay) {
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
  }

  return nextBillingDate.getTime();
}

/**
 * Calculate the amount for the first installment and the remaining installments for yearly subscriptions
 * @param items The items in the subscription
 * @param installments The number of installments
 * @param billingDay The day of the month for billing
 * @param creditDiscount The credit discount to apply
 * @param endDate The end date of the subscription
 * @param today Optional date to use instead of the current date (useful for testing)
 * @returns The amount for the first installment and the remaining installments
 */
export function calculateYearlyInstallmentsAmount(
  items: { totalPrice: number }[],
  installments: number,
  billingDay: number,
  creditDiscount: number,
  endDate: number,
  paymentMethod?: PaymentMethod,
  today?: number,
) {
  today ??= currentTimestamp();

  const daysInCycle = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));

  const daysUntilBilling = billingDay - getDate(today);

  const totalAmount = items.reduce((acc, item) => acc + item.totalPrice, 0);

  if (paymentMethod === PaymentMethod.CREDIT_CARD) {
    return [totalAmount, 0];
  }

  if (installments === 1) {
    return [totalAmount, 0];
  }

  const amountPerDay = totalAmount / daysInCycle;

  let firstInstallmentAmount = daysUntilBilling > 0
    ? amountPerDay * daysUntilBilling
    : (amountPerDay * daysInCycle) / installments;

  const amountPerInstallment = totalAmount / installments;

  const remainingCredit = Math.max(0, creditDiscount - firstInstallmentAmount);

  const remainingInstallmentAmount = amountPerInstallment - (remainingCredit / (installments - 1));

  // If credit is enough to cover the first installment, return 0 for the first installment and the rest of the credit for the remaining installments
  if (creditDiscount >= firstInstallmentAmount) {
    return [0, Math.round(remainingInstallmentAmount)];
  }

  return [Math.round(firstInstallmentAmount), Math.round(remainingInstallmentAmount)];
}

class Cycle {
  startDate: number;
  endDate: number | null;
}

export const createSubscriptionPlan = ({
  plan,
  createPaymentDto,
  account,
  status = QISubscriptionStatus.PENDING,
  customerId,
  cycle,
}: {
  plan: Plan,
  createPaymentDto: CreatePaymentDto,
  account: Account,
  status?: QISubscriptionStatus,
  customerId?: string,
  cycle?: Cycle,
}): Omit<QISubscription, 'id'> | null => {

  let { isYearly, billingDay, installments, clientTimestamp: userTime } = createPaymentDto;
  const currentTimestamp = new Date(userTime).getTime();

  // O dia do vencimento só é considerado para assinaturas anuais com mais de 1 parcela
  billingDay = isYearly && installments > 1 ? billingDay : getDate(currentTimestamp);

  plan = plan.updateFromCreatePaymentDto(createPaymentDto);

  const option = plan.option || plan.options[0];

  const planValue = plan.planValue(isYearly);
  const items: QISubscriptionItem[] = [
    {
      id: 'plan',
      name: plan.name,
      type: 'plan',
      included: 0,
      quantity: 1,
      unitPrice: planValue,
      totalPrice: planValue,
    },
  ];

  if (plan.additionals.includes('shotx-module')) {
    items.push({
      id: 'shotx-module',
      name: 'Shotx',
      type: 'module',
      included: 0,
      quantity: 1,
      unitPrice: plan.shotxValue,
      totalPrice: plan.shotxValue,
    });
  }

  plan.customFeatures.forEach((feature) => {
    const unitPrice = plan.isYearly
      ? feature.yearlyPrice * 12
      : feature.monthlyPrice;
    const totalPrice = unitPrice * (feature.quantity - feature.included);
    items.push({
      id: feature.id,
      type: 'addon',
      name: feature.name,
      included: feature.included,
      quantity: feature.quantity,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
    });
  });

  let { startDate, endDate } = cycle || { startDate: currentTimestamp, endDate: null };

  const isDifferentBillingDay = billingDay !== getDate(startDate);

  // Se a data de vencimento for diferente da data de início, calcula a data final com base no número de parcelas
  if (isDifferentBillingDay) {
    // Data da última cobrança
    const lastBillingDate = calculateInstallmentDueDate((installments + 1), billingDay, startDate);
    endDate = lastBillingDate;
  } else {
    endDate ??= isYearly ? addYears(startDate, 1) : addDays(startDate, 30);
  }

  const billingInterval = isYearly
    ? QIBillingInterval.YEARLY
    : QIBillingInterval.MONTHLY;

  // Calculate billing date based on subscription type
  const billingDate: number = isYearly
    ? Number(installments) === 1
      ? endDate
      : calculateYearlyBillingDate(billingDay)
    : addDays(startDate, 30);

  const gateway = PaymentGateway[createPaymentDto.paymentMethod];

  const subscription = {
    gateway,
    accountId: account.id!,
    status,
    planId: plan.id,
    paymentMethod: createPaymentDto.paymentMethod as unknown as QIPaymentMethod,
    items,
    billingInterval: billingInterval,
    billingDay: billingDay,
    startDate: toFirebaseTimestamp(startDate),
    cycle: 1,
    installments: Number(createPaymentDto.installments),
    currentPeriodStart: toFirebaseTimestamp(startDate),
    currentPeriodEnd: toFirebaseTimestamp(endDate),
    createdAt: currentTimestamp,
    updatedAt: currentTimestamp,
    nextBillingDate: toFirebaseTimestamp(billingDate),
    currency: 'BRL',
    accountConfig: {
      data: option,
      modules: plan.modules,
      config: {
        ...plan.customFeatures.reduce(
          (acc, feature) => {
            acc[feature.id] = feature.quantity;
            return acc;
          },
          {} as Record<string, number>,
        ),
      },
    },
    customerId,
  } as Omit<QISubscription, 'id'>;

  // If is yearly and have endDate put this to subscription
  if (isYearly && endDate) {
    subscription['endDate'] = toFirebaseTimestamp(endDate)
  }

  console.log('Assinatura criada', {
    inicio: subscription.startDate.toDate(),
    fim: subscription.currentPeriodEnd.toDate(),
    vencimento: subscription.nextBillingDate.toDate(),
    intervalo: subscription.billingInterval,
    parcelas: subscription.installments,
    diaVencimento: subscription.billingDay,
    inicioCiclo: subscription.currentPeriodStart.toDate(),
    fimCiclo: subscription.currentPeriodEnd?.toDate(),
  })

  return subscription;
};

export const createSubscriptionPlanPreview = (
  plan: Plan,
  previewData: UpgradePreviewDto | PlanPreviewDto,
  account: Account,
  status: QISubscriptionStatus = QISubscriptionStatus.PENDING,
  paymentMethod: QIPaymentMethod,
  startDate: number = currentTimestamp(),
  endDate?: number,
): Omit<QISubscription, 'id'> => {
  const { isYearly, billingDay } = previewData;
  plan = plan.updateFromCreatePaymentDto(previewData);

  const option = plan.option || plan.options[0];

  const planValue = plan.planValue(isYearly);
  const items: QISubscriptionItem[] = [
    {
      id: 'plan',
      name: plan.name,
      type: 'plan',
      included: 0,
      quantity: 1,
      unitPrice: planValue,
      totalPrice: planValue,
    },
  ];

  if (plan.additionals.includes('shotx-module')) {
    items.push({
      id: 'shotx-module',
      name: 'Shotx',
      type: 'module',
      included: 0,
      quantity: 1,
      unitPrice: plan.shotxValue,
      totalPrice: plan.shotxValue,
    });
  }

  plan.customFeatures.forEach((feature) => {
    const unitPrice = plan.isYearly
      ? feature.yearlyPrice * 12
      : feature.monthlyPrice;
    const totalPrice = unitPrice * (feature.quantity - feature.included);
    items.push({
      id: feature.id,
      type: 'addon',
      name: feature.name,
      included: feature.included,
      quantity: feature.quantity,
      unitPrice: unitPrice,
      totalPrice: totalPrice,
    });
  });

  endDate ??= isYearly
    ? addYears(startDate, 1)
    : addDays(startDate, 30);

  const gateway = PaymentGateway[paymentMethod];

  const installments =
    previewData instanceof PlanPreviewDto
      ? 1
      : Number(previewData.installments);

  return {
    gateway,
    accountId: account.id!,
    status,
    planId: plan.id,
    paymentMethod,
    items,
    billingInterval: isYearly
      ? QIBillingInterval.YEARLY
      : QIBillingInterval.MONTHLY,

    billingDay: billingDay,
    startDate: toFirebaseTimestamp(startDate),
    cycle: 1,
    installments,
    currentPeriodStart: toFirebaseTimestamp(startDate),
    currentPeriodEnd: toFirebaseTimestamp(endDate),
    createdAt: new Date().getTime(),
    updatedAt: new Date().getTime(),
    // Calculate next billing date based on the same rules as createSubscriptionPlan
    nextBillingDate: toFirebaseTimestamp(
      isYearly ? installments === 1 ? endDate : calculateYearlyBillingDate(billingDay) : addDays(startDate, 30)
    ),
    currency: 'BRL',
    accountConfig: {
      data: option,
      modules: plan.modules,
      config: {
        ...plan.customFeatures.reduce(
          (acc, feature) => {
            acc[feature.id] = feature.quantity;
            return acc;
          },
          {} as Record<string, number>,
        ),
      },
    },
  };
};
