import {
  Body,
  Controller,
  Post,
  Req,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentService } from './payment.service';
import { ApiBearerAuth } from '@nestjs/swagger';
import { FirebaseAccountGuard } from '../auth/firebase-account.guard';
import { MAX_TIMEZONE_DIFF } from 'src/utils/date.utils';

@Controller('/payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) { }

  @Post('subscribe')
  @ApiBearerAuth()
  @UseGuards(FirebaseAccountGuard)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  subscribe(@Body() createPaymentDto: CreatePaymentDto, @Req() request: any) {
    const clientDateTime = new Date(createPaymentDto.clientTimestamp).getTime();
    const serverDateTime = new Date().getTime();

    // Verificar se a data do cliente tem no máximo 24h de diferença tanto para o futuro quanto para o passado
    if (clientDateTime > serverDateTime + MAX_TIMEZONE_DIFF || clientDateTime < serverDateTime - MAX_TIMEZONE_DIFF) {
      return { message: 'Timestamp inválido. Diferença de 24h excede o limite de 24h' }
    }

    return this.paymentService.subscribe(createPaymentDto, request.account);
  }
}
